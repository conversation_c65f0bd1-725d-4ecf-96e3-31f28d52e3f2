import _ from "lodash";

import type { SearchSimpleModelType } from "^/types/class-management";
import type { PaginationParamsOption } from "@/helps/api";

import { dateFormatSTZ } from "@/helps/date";
import { namespaceT } from "@/helps/namespace-t";

type ListParamsType = SearchSimpleModelType & PaginationParamsOption;

/** 处理时间数据为 YYYY-MM-DD */
const formateToDate = (date: string | Date) => {
  const t = namespaceT('dateFormat');
  return dateFormatSTZ(new Date(date), t('date'));
};

export const handleListParams = (params:ListParamsType)=>{
  const cloneParams= _.cloneDeep(params);
  
  if(cloneParams.creStartTime && cloneParams.creEndTime){
    cloneParams.creStartTime=formateToDate(cloneParams.creStartTime)
    cloneParams.creEndTime=formateToDate(cloneParams.creEndTime)
  }

  return cloneParams

}


export const handleListParamsWithPage = (params:ListParamsType)=>{
  const cloneParams= handleListParams(params);

  Object.assign(cloneParams,{ 
    page: cloneParams.page.page,
    limit: cloneParams.page.limit
  })

  return cloneParams
}