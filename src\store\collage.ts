import { ref } from 'vue';
import { defineStore } from 'pinia';

export const useCollageStore = defineStore('collageStore', () => {
  const loading = ref(false);
  const loaded = ref(false);
  const data = ref([]);

  const loadDataIfNeeded = async () => {
    if (!loading.value || !loaded.value) {
      return;
    }

    try {
      const res = await new Promise<unknown[]>((resolve) => {
        setTimeout(() => {
          resolve([]);
        }, 3000);
      });

      // const api = new  API();
      // const res = await api.send();

      data.value = res;

      loaded.value = true;

    } catch (error) {
      loaded.value = false;
      throw error;
    }finally{
      loading.value = false;
    }
  }

  return {
    loading,
    loaded,
    data,

    loadDataIfNeeded,
  }

})