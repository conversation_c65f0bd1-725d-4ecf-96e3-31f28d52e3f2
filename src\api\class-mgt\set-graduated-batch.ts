import { CommonApi, RequestMethod } from '@/api/common/common-api';
import { BaseError } from '@/errors/base-error';
import { namespaceT } from '@/helps/namespace-t';

//  INVALID_FIELD:请选择需要处理的班级 STATUS_ABNORMAL:只能设置班级情况为学生在读的数据为毕业班
enum ErrorCodes {
  INVALID_FIELD = 'INVALID_FIELD',
  STATUS_ABNORMAL = 'STATUS_ABNORMAL',
}

export class ClassMgtSetGraduatedBatchApi<T> extends CommonApi<T> {

  url() {
    return `/classes/graduate-batch`;
  }
  
  method(): RequestMethod {
    return "POST"
  }

  async send(): Promise<T> {
    try {
      const res = await super.send();
      return res;
    } catch (error) {
      const t = namespaceT('apiErrors.classMgt.setGraduatedBatch');
      
      switch (error.code) {
        case ErrorCodes.INVALID_FIELD:
        case ErrorCodes.STATUS_ABNORMAL:
          throw new BaseError(t(error.code));
        default:
          throw error;
      }

    }  
  }   
}
