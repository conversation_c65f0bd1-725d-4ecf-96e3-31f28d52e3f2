<script setup lang="ts">
import { namespaceT } from '@/helps/namespace-t';


const emit = defineEmits<{
  'on-set-graduate':[],
  'on-export':[]
}>();


const t = namespaceT('classMgt');

</script>


<template>
  <Button
    class="pima-btn mr-15"
    type="primary"
    @click="emit('on-set-graduate')"
  >
    {{ t('action.setGraduate') }}
  </Button>

  <Button
    class="pima-btn"
    type="primary"
    @click="emit('on-export')"
  >
    {{ t('action.exportClass') }}
  </Button>
</template>
