<script setup lang="ts">
import type { SearchSimpleModelType } from '^/types/class-management';

import WrapperSearchSimple from '@/components/common/wrapper-search-simple.vue';
import PairLabelItem from '@/components/common/pair-label-item.vue';
import InputSearch from '@/components/common/input-search.vue';
import PickDateRange from '@/components/common/picker/date-range.vue';
import PickCollage from '@/components/biz/select/collage.vue';

import { namespaceT } from '@/helps/namespace-t';

const emit = defineEmits<{
  'on-search': [],
  'on-add': []
}>();

const t = namespaceT('classMgt');
const model = defineModel<SearchSimpleModelType>();

const emitSearch = () => {
  emit('on-search');
};
</script>


<template>
  <WrapperSearchSimple>
    <PairLabelItem :label="t('label.collage')">
      <PickCollage
        v-model="model.facId"
        class="w-250 "
        clearable
        @on-change="emitSearch"
      />
    </PairLabelItem>

    <PairLabelItem :label="t('label.createTime')">
      <PickDateRange
        v-model:min="model.creStartTime"
        v-model:max="model.creEndTime"
        @on-change="emitSearch"
      />
    </PairLabelItem>


    <PairLabelItem no-colon>
      <InputSearch
        v-model.trim="model.keyword"
        :placeholder="t('placeholder.keyword')"
        class="w-300"
        clearable
        @on-clear="emitSearch"
        @on-search="emitSearch"
      />
    </PairLabelItem>
  </WrapperSearchSimple>
</template>


<style scoped lang='less'>
:deep(.pima-pair-label-item) {
  margin-left: 20px;
}
</style>
