export default {
  label: {
    collage: '所属学院',
    createTime: '@:common.table.createdTime',

    classBelongCollage: '班级所属学院',
    isGraduated: '班级是否已毕业',
  },

  columns: {
    collage: '@:classMgt.label.classBelongCollage',
    className: '班级名称',
    tutor: '班导师',
    numOfClass: '班级人数',
    situation: '班级情况',
    operatorAndTime: '操作人/时间',
  },

  placeholder: {
    keyword: '请输入班级名称',
  },

  text: {
    checkBox: {
      selectAll: '全选所有列表结果（共{total}条申请记录）',
    },
    popTips: '勾选时表示选中当前搜索结果中的所有可勾选的数据，包括本页之后的数据',
    className: '班级名称开头需是班级学生入学年份',
  },

  hint: {
    selectData: '请选择需要处理的班级',
  },


  action: {
    setGraduate: '设为毕业班',
    exportClass: '导出班级',
    edit: '@:common.action.edit',
    delete: '@:common.action.delete',
  },

  modal: {

    setGraduate: {
      title: '设为毕业班',
      content: '班级设为毕业班之后，后续将无法进行编辑，是否继续？',
    },

    delete: {
      content: '该班级存在学生信息，请先前往“学生资料”菜单，移除班级中的学生，再进行删除操作！',
    },

    edit: {
      title: '@:classMgt.action.edit',
    },
  },

};
