<script setup lang="ts">
import { onBeforeMount } from 'vue';

import { useCollageStore } from '@/store/collage';


const store = useCollageStore()

onBeforeMount(() => {
  store.loadDataIfNeeded();
});

</script>


<template>
  <Select
    class="pima-select"
    :loading="store.loading"
    v-bind="$attrs"
  >
    <Option
      v-for="item in store.data"
      :key="item.value"
      :value="item.value"
      :label="item.label"
    />
  </Select>
</template>
