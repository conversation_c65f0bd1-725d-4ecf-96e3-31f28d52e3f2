<script setup lang="ts">
import { defineAsyncComponent } from 'vue';

import ClientOnly from '../client-only';

defineOptions({
  name: 'PimaImportExportCenter',
});


// eslint-disable-next-line import/no-unresolved, @typescript-eslint/no-unused-vars
const ImportExportCenter = defineAsyncComponent(() => import('pimaRemoteUI/PimaImportExportCenter'));
</script>

<template>
  <ClientOnly>
    <ImportExportCenter v-bind="$attrs" />
  </ClientOnly>
</template>
