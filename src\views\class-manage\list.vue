<script setup lang="ts">
import { ref, reactive } from 'vue'

import TitleBar from '@/components/common/title-bar.vue'
import WaringModal from '@/components/biz/modal/waring-modal.vue'
import TitleButton from './components/title-button.vue'
import SearchSimple from './components/search-simple.vue'

import { ClassMgtListApi } from '@/api/class-mgt/list'
import { ClassMgtExportApi } from '@/api/class-mgt/export'
import { ClassMgtSetGraduatedBatchApi } from '@/api/class-mgt/set-graduated-batch'
import { namespaceT } from '@/helps/namespace-t'
import { createWarningModel } from '@/helps/models'
import { openToastError, openToastSuccess } from '@/helps/toast'
import { useSider } from '@/uses/sider'
import { useQueryTable } from '@/uses/query-table'
import { useTableLoader } from '@/uses/table-loader'
import { useImportExport } from '@/uses/import-export'

import { createSearchSimpleModel } from './helps/models'
import {
	handleListParams,
	handleListParamsWithPage,
} from './helps/handle-api-data'

defineOptions({
	name: 'ClassMgtList',
})

const t = namespaceT('classMgt')

const setting = ref(false)
const batchSetGraduateModel = reactive(
	createWarningModel({
		title: t('modal.setGraduate.title'),
		content: t('modal.setGraduate.content'),
	})
)

const { getMenuName } = useSider()
const loadData = useTableLoader(ClassMgtListApi, handleListParams)

const qt = useQueryTable({
	load: loadData,
	simpleSearchModel: createSearchSimpleModel(),
})

const onExport = async () => {
	const api = new ClassMgtExportApi<{ model: number }>()
	api.params = handleListParamsWithPage(qt.query.value)
	const data = await api.send()
	return { taskId: data.model }
}

const importExportCenter = useImportExport({
	exportData: onExport,
})

const onSetGraduateBatch = async () => {
	try {
		setting.value = true
		const api = new ClassMgtSetGraduatedBatchApi()
		api.params = handleListParamsWithPage(qt.query.value)
		await api.send()
		// openToastSuccess()
	} catch (error) {
		openToastError(error.message)
	} finally {
		setting.value = false
	}
}

const onShowBatchModal = () => {
	batchSetGraduateModel.visible = true
}
</script>

<template>
	<TitleBar :title="getMenuName((SMC) => SMC.ClassManagement)">
		<template #right>
			<TitleButton
				:exporting="importExportCenter.exporting"
				@on-export="importExportCenter.exportData"
				@on-set-graduate="onShowBatchModal"
			/>
		</template>
	</TitleBar>

	<SearchSimple v-model="qt.simpleSearchModel" @on-search="qt.load" />

	<WaringModal
		v-model="batchSetGraduateModel.visible"
		:loading="batchSetGraduateModel.loading"
		:title="batchSetGraduateModel.title"
		:content="batchSetGraduateModel.content"
		@on-confirm="onSetGraduateBatch"
	/>
</template>
