import { CommonApi, RequestMethod } from '@/api/common/common-api';
import { BaseError } from '@/errors/base-error';
import { namespaceT } from '@/helps/namespace-t';

enum ErrorCodes {
  INVALID_FIELD = 'INVALID_FIELD',
  STATUS_ABNORMAL = 'STATUS_ABNORMAL',
}

export class ClassMgtSetGraduatedApi<T> extends CommonApi<T> {
  id:number;

  constructor({id}){
    super()
    this.id = id;
  }

  url() {
    return `/classes/${this.id}/graduate`;
  }
  
  method(): RequestMethod {
    return "POST"
  }

  async send(): Promise<T> {
    try {
      const res = await super.send();
      return res;
    } catch (error) {
      const t = namespaceT('apiErrors.classMgt.setGraduated');
      
      switch (error.code) {
        case ErrorCodes.INVALID_FIELD:
        case ErrorCodes.STATUS_ABNORMAL:
          throw new BaseError(t(error.code));
        default:
          throw error;
        }
    }
  }
}
